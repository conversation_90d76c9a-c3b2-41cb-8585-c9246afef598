#ifndef TTKFUNCTIONLISTHWIDGET_H
#define TTKFUNCTIONLISTHWIDGET_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include <QWidget>
#include "ttkmoduleexport.h"

/*!
 * <AUTHOR> <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKFunctionItemWidget : public QWidget
{
    Q_OBJECT
    TTK_DECLARE_MODULE(TTKFunctionItemWidget)
public:
    explicit TTKFunctionItemWidget(QWidget *parent = nullptr);

    void setLabelText(const QString &text);
    void setLabelIcon(const QString &iconf, const QString &iconb);

    void setSelectedMode(bool select);
    void resizeMode(bool mode);

Q_SIGNALS:
    void selectedChanged(TTKFunctionItemWidget *item);

private:
    virtual void mousePressEvent(QMouseEvent *event) override final;
    virtual void enterEvent(QtEnterEvent *event) override final;
    virtual void leaveEvent(QEvent *event) override final;
    virtual void paintEvent(QPaintEvent *event) override final;

    QString m_text, m_iconf, m_iconb;
    bool m_enterIn, m_selectedOn, m_resizeMode;

};


/*!
 * <AUTHOR> <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKFunctionListHWidget : public QWidget
{
    Q_OBJECT
    TTK_DECLARE_MODULE(TTKFunctionListHWidget)
public:
    explicit TTKFunctionListHWidget(QWidget *parent = nullptr);
    ~TTKFunctionListHWidget();

    void resizeMode(bool mode);

Q_SIGNALS:
    void currentIndexChanged(int index);

public Q_SLOTS:
    void selectedChanged(TTKFunctionItemWidget *item);

private:
    virtual void mousePressEvent(QMouseEvent *event) override final;

    QList<TTKFunctionItemWidget*> m_items;

};

#endif // TTKFUNCTIONLISTHWIDGET_H
