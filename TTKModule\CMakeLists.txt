# ***************************************************************************
# * This file is part of the TTK Widget Tools project
# * Copyright (C) 2015 - 2025 Greedysky Studio
#
# * This program is free software; you can redistribute it and/or modify
# * it under the terms of the GNU Lesser General Public License as published by
# * the Free Software Foundation; either version 3 of the License, or
# * (at your option) any later version.
#
# * This program is distributed in the hope that it will be useful,
# * but WITHOUT ANY WARRANTY; without even the implied warranty of
# * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# * GNU Lesser General Public License for more details.
#
# * You should have received a copy of the GNU Lesser General Public License along
# * with this program; If not, see <http://www.gnu.org/licenses/>.
# ***************************************************************************

cmake_minimum_required(VERSION 3.0.0)

project(TTKCore)

add_subdirectory(Button)
add_subdirectory(Label)
add_subdirectory(LineEdit)
add_subdirectory(Meter)
add_subdirectory(Progress)
add_subdirectory(Slider)
add_subdirectory(Title)
add_subdirectory(Widget)
add_subdirectory(Window)


get_property(TTK_MODULE_BUTTON_INCLUDE_FILES GLOBAL PROPERTY "TTK_MODULE_BUTTON_INCLUDE_FILES")
get_property(TTK_MODULE_LABEL_INCLUDE_FILES GLOBAL PROPERTY "TTK_MODULE_LABEL_INCLUDE_FILES")
get_property(TTK_MODULE_PROGRESS_INCLUDE_FILES GLOBAL PROPERTY "TTK_MODULE_PROGRESS_INCLUDE_FILES")
get_property(TTK_MODULE_TITLE_INCLUDE_FILES GLOBAL PROPERTY "TTK_MODULE_TITLE_INCLUDE_FILES")

include_directories(
  ${TTK_MODULE_BUTTON_INCLUDE_FILES}
  ${TTK_MODULE_LABEL_INCLUDE_FILES}
  ${TTK_MODULE_PROGRESS_INCLUDE_FILES}
  ${TTK_MODULE_TITLE_INCLUDE_FILES}
)

get_property(TTK_MODULE_BUTTON_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_BUTTON_HEADER_FILES")
get_property(TTK_MODULE_LABEL_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_LABEL_HEADER_FILES")
get_property(TTK_MODULE_LINEEDIT_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_LINEEDIT_HEADER_FILES")
get_property(TTK_MODULE_METER_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_METER_HEADER_FILES")
get_property(TTK_MODULE_PROGRESS_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_PROGRESS_HEADER_FILES")
get_property(TTK_MODULE_SLIDER_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_SLIDER_HEADER_FILES")
get_property(TTK_MODULE_TITLE_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_TITLE_HEADER_FILES")
get_property(TTK_MODULE_WIDGET_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_WIDGET_HEADER_FILES")
get_property(TTK_MODULE_WINDOW_HEADER_FILES GLOBAL PROPERTY "TTK_MODULE_WINDOW_HEADER_FILES")

set(HEADER_FILES
  ${TTK_MODULE_BUTTON_HEADER_FILES}
  ${TTK_MODULE_LABEL_HEADER_FILES}
  ${TTK_MODULE_LINEEDIT_HEADER_FILES}
  ${TTK_MODULE_METER_HEADER_FILES}
  ${TTK_MODULE_PROGRESS_HEADER_FILES}
  ${TTK_MODULE_SLIDER_HEADER_FILES}
  ${TTK_MODULE_TITLE_HEADER_FILES}
  ${TTK_MODULE_WIDGET_HEADER_FILES}
  ${TTK_MODULE_WINDOW_HEADER_FILES}
)

get_property(TTK_MODULE_BUTTON_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_BUTTON_SOURCE_FILES")
get_property(TTK_MODULE_LABEL_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_LABEL_SOURCE_FILES")
get_property(TTK_MODULE_LINEEDIT_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_LINEEDIT_SOURCE_FILES")
get_property(TTK_MODULE_METER_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_METER_SOURCE_FILES")
get_property(TTK_MODULE_PROGRESS_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_PROGRESS_SOURCE_FILES")
get_property(TTK_MODULE_SLIDER_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_SLIDER_SOURCE_FILES")
get_property(TTK_MODULE_TITLE_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_TITLE_SOURCE_FILES")
get_property(TTK_MODULE_WIDGET_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_WIDGET_SOURCE_FILES")
get_property(TTK_MODULE_WINDOW_SOURCE_FILES GLOBAL PROPERTY "TTK_MODULE_WINDOW_SOURCE_FILES")

set(SOURCE_FILES
  ${TTK_MODULE_BUTTON_SOURCE_FILES}
  ${TTK_MODULE_LABEL_SOURCE_FILES}
  ${TTK_MODULE_LINEEDIT_SOURCE_FILES}
  ${TTK_MODULE_METER_SOURCE_FILES}
  ${TTK_MODULE_PROGRESS_SOURCE_FILES}
  ${TTK_MODULE_SLIDER_SOURCE_FILES}
  ${TTK_MODULE_TITLE_SOURCE_FILES}
  ${TTK_MODULE_WIDGET_SOURCE_FILES}
  ${TTK_MODULE_WINDOW_SOURCE_FILES}
)

get_property(TTK_MODULE_BUTTON_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_BUTTON_QRC_FILES")
get_property(TTK_MODULE_LABEL_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_LABEL_QRC_FILES")
get_property(TTK_MODULE_PROGRESS_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_PROGRESS_QRC_FILES")
get_property(TTK_MODULE_TITLE_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_TITLE_QRC_FILES")
get_property(TTK_MODULE_WIDGET_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_WIDGET_QRC_FILES")
get_property(TTK_MODULE_WINDOW_QRC_FILES GLOBAL PROPERTY "TTK_MODULE_WINDOW_QRC_FILES")

set(QRC_FILES
  ${TTK_MODULE_BUTTON_QRC_FILES}
  ${TTK_MODULE_LABEL_QRC_FILES}
  ${TTK_MODULE_PROGRESS_QRC_FILES}
  ${TTK_MODULE_TITLE_QRC_FILES}
  ${TTK_MODULE_WIDGET_QRC_FILES}
  ${TTK_MODULE_WINDOW_QRC_FILES}
)

if(WIN32)
  list(APPEND SOURCE_FILES ${PROJECT_NAME}.rc)
endif()

set(QT_LINK_LIBS TTKLibrary)

if(WIN32)
  list(APPEND QT_LINK_LIBS Iphlpapi)
endif()

if(TTK_QT_VERSION VERSION_EQUAL "4")
  qt4_wrap_cpp(MOC_FILES ${HEADER_FILES})
  qt4_add_resources(RCC_FILES ${QRC_FILES})

  list(APPEND QT_LINK_LIBS ${QT_QTCORE_LIBRARY} ${QT_QTGUI_LIBRARY})
elseif(TTK_QT_VERSION VERSION_EQUAL "5")
  qt5_wrap_cpp(MOC_FILES ${HEADER_FILES})
  qt5_add_resources(RCC_FILES ${QRC_FILES})

  list(APPEND QT_LINK_LIBS Qt5::Core Qt5::Gui Qt5::Widgets)
elseif(TTK_QT_VERSION VERSION_EQUAL "6")
  qt6_wrap_cpp(MOC_FILES ${HEADER_FILES})
  qt6_add_resources(RCC_FILES ${QRC_FILES})

  list(APPEND QT_LINK_LIBS Qt6::Core Qt6::Gui Qt6::Widgets Qt6::StateMachine)
endif()

if(TTK_BUILD_SHARED)
  add_library(${PROJECT_NAME} SHARED ${SOURCE_FILES} ${HEADER_FILES} ${MOC_FILES} ${RCC_FILES})
else()
  add_library(${PROJECT_NAME} STATIC ${SOURCE_FILES} ${HEADER_FILES} ${MOC_FILES} ${RCC_FILES})
endif()
target_link_libraries(${PROJECT_NAME} ${QT_LINK_LIBS})
