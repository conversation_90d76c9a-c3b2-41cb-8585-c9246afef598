<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TTKToolsApplication</class>
 <widget class="QWidget" name="TTKToolsApplication">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1001</width>
    <height>669</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>TTKWidgetTools</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../TTKUi/TTKModule.qrc">
    <normaloff>:/image/lb_app_logo</normaloff>:/image/lb_app_logo</iconset>
  </property>
  <layout class="QVBoxLayout" name="mainWidgetLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="background">
     <property name="pixmap">
      <pixmap resource="../TTKUi/TTKModule.qrc">:/image/lb_background</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="backgroundLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>1</number>
      </property>
      <property name="topMargin">
       <number>1</number>
      </property>
      <property name="rightMargin">
       <number>1</number>
      </property>
      <property name="bottomMargin">
       <number>1</number>
      </property>
      <item>
       <widget class="QWidget" name="topWidget" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="label">
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap resource="../TTKUi/TTKModule.qrc">:/image/lb_app_logo</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelName">
           <property name="styleSheet">
            <string notr="true">color:white;font-size:15px;</string>
           </property>
           <property name="text">
            <string>TTKWidgetTools</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QToolButton" name="minimization">
           <property name="minimumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="windowClose">
           <property name="minimumSize">
            <size>
             <width>18</width>
             <height>18</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>18</width>
             <height>18</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="TTKBackgroundWidget" name="centerWidget" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>4</number>
         </property>
         <property name="topMargin">
          <number>4</number>
         </property>
         <property name="rightMargin">
          <number>4</number>
         </property>
         <property name="bottomMargin">
          <number>4</number>
         </property>
         <item>
          <widget class="TTKFunctionToolBoxWidget" name="functionListWidget" native="true">
           <property name="minimumSize">
            <size>
             <width>280</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>280</width>
             <height>16777215</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSplitter" name="widget">
           <property name="lineWidth">
            <number>1</number>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="handleWidth">
            <number>1</number>
           </property>
           <widget class="TTKBackgroundContainer" name="containerWidget" native="true"/>
           <widget class="TTKPropertyContainWidget" name="propertyWidget" native="true">
            <property name="minimumSize">
             <size>
              <width>280</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>TTKFunctionToolBoxWidget</class>
   <extends>QWidget</extends>
   <header>ttkfunctiontoolboxwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>TTKBackgroundContainer</class>
   <extends>QWidget</extends>
   <header>ttkbackgroundcontainer.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>TTKPropertyContainWidget</class>
   <extends>QWidget</extends>
   <header>ttkpropertycontainwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>TTKBackgroundWidget</class>
   <extends>QWidget</extends>
   <header>ttkbackgroundwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../TTKUi/TTKModule.qrc"/>
 </resources>
 <connections/>
</ui>
