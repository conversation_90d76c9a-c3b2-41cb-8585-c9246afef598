# Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

name: win10

on: workflow_call

env:
  TTK_MODULE: TTKWidgetTools
  TTK_VERSTION: 3.1.0.0

jobs:
  build:
    name: Build on win64 Qt6
    strategy:
      matrix:
        qt_version: [6.5.3]
        os: [windows-latest]

    runs-on: ${{matrix.os}}
    steps:
      - uses: actions/checkout@v4

      - name: Cache Qt
        uses: actions/cache@v4
        with:
          path: ${{runner.workspace}}/Qt
          key: ${{runner.os}}-QtCache-${{matrix.qt_version}}

      - name: Install Qt
        uses: jurplel/install-qt-action@v4
        with:
          version: ${{matrix.qt_version}}
          host: windows
          arch: win64_mingw
          target: desktop
          modules: qt5compat qtscxml
          cache: 'true'

      - name: Create build dir
        shell: bash
        run: mkdir build
        working-directory: ${{runner.workspace}}

      - name: Build project
        shell: cmd
        run: |
          qmake -v
          cmake --version
          cmake %GITHUB_WORKSPACE% -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=${{runner.workspace}}/install -DTTK_QT_VERSION=6
          make && make install
        working-directory: ${{runner.workspace}}/build

      - name: Build package
        run: |
          windeployqt ${{env.TTK_MODULE}}/${{env.TTK_VERSTION}}/${{env.TTK_MODULE}}.exe
          cp ${{runner.workspace}}/Qt/${{matrix.qt_version}}/mingw_64/bin/Qt6Xml.dll ${{env.TTK_MODULE}}/${{env.TTK_VERSTION}}
          cp ${{runner.workspace}}/Qt/${{matrix.qt_version}}/mingw_64/bin/Qt6StateMachine.dll ${{env.TTK_MODULE}}/${{env.TTK_VERSTION}}
          7z a ${{env.TTK_MODULE}}-${{env.TTK_VERSTION}}-win10-x64.7z ${{env.TTK_MODULE}}
        working-directory: ${{runner.workspace}}/install

      - name: Archive artifacts
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{env.TTK_VERSTION}}
          token: ${{secrets.GITHUB_TOKEN}}
          generate_release_notes: false
          files: ${{runner.workspace}}/install/${{env.TTK_MODULE}}-${{env.TTK_VERSTION}}-win10-x64.7z
