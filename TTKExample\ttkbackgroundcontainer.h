#ifndef TTK<PERSON><PERSON><PERSON>GROUNDCONTAINER_H
#define TTKBAC<PERSON><PERSON>OUNDCONTAINER_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include "ttkgrabitemwidget.h"

class TTK_MODULE_EXPORT TTKBackgroundContainerItem : public TTKGrabItemWidget
{
    Q_OBJECT
public:
    explicit TTKBackgroundContainerItem(QWidget *parent = nullptr);
    ~TTKBackgroundContainerItem();

    void addItem(QWidget *item);

private Q_SLOTS:
    virtual void onMouseChange(int x, int y) override final;

private:
    virtual void paintEvent(QPaintEvent *event) override final;

    QWidget *m_item;

};


class TTK_MODULE_EXPORT TTKBackgroundContainer : public QWidget
{
    Q_OBJECT
public:
    explicit TTKBackgroundContainer(QWidget *parent = nullptr);
    ~TTKBackgroundContainer();

    void addItem(QWidget *item);

private:
    virtual void paintEvent(QPaintEvent *event) override final;

    TTKBackgroundContainerItem *m_item;

};

#endif // TTKBACKGROUNDCONTAINER_H
