#ifndef TTKFUNCTIONNORMALWIDGET_H
#define TTKFUNCTIONNORMALWIDGET_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include <QLabel>
#include "ttkmoduleexport.h"

class QTimer;

/*!
 * <AUTHOR> <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKFunctionNormalWidget : public QWidget
{
    Q_OBJECT
    TTK_DECLARE_MODULE(TTKFunctionNormalWidget)
public:
    explicit TTKFunctionNormalWidget(QWidget *parent = nullptr);

    void setSize(qreal w, qreal h);
    void addItem(const QString &text);

private Q_SLOTS:
    void updateRender();

private:
    virtual void paintEvent(QPaintEvent *event) override final;
    virtual void mouseMoveEvent(QMouseEvent *event) override final;
    virtual void mousePressEvent(QMouseEvent *event) override final;

    void drawItem(QPainter *painter);
    void drawChooseItem(QPainter* painter);
    void drawListWidget(QPainter* painter);

    int m_height, m_width;
    int m_backGroundColor, m_oldChooseBackGround, m_newChooseBackGround;

    int m_itemOffset;
    int m_chooseIndex;

    QTimer *m_timer;
    QVector<QString> m_items;

};

#endif // TTKFUNCTIONNORMALWIDGET_H
