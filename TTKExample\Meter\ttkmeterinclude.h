#ifndef TTKMETERINCLUDE_H
#define TTKMETERINCLUDE_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include "arcMeterWidget/ttkarcmeterwidgetproperty.h"
#include "carMeterWidget/ttkcarmeterwidgetproperty.h"
#include "clockMeterWidget/ttkclockmeterwidgetproperty.h"
#include "compassMeterWidget/ttkcompassmeterwidgetproperty.h"
#include "dialMeterWidget/ttkdialmeterwidgetproperty.h"
#include "miniMeterWidget/ttkminimeterwidgetproperty.h"
#include "paintMeterWidget/ttkpaintmeterwidgetproperty.h"
#include "panelMeterWidget/ttkpanelmeterwidgetproperty.h"
#include "percentMeterWidget/ttkpercentmeterwidgetproperty.h"
#include "progressMeterWidget/ttkprogressmeterwidgetproperty.h"
#include "radarMeterWidget/ttkradarmeterwidgetproperty.h"
#include "roundMeterWidget/ttkroundmeterwidgetproperty.h"
#include "speedMeterWidget/ttkspeedmeterwidgetproperty.h"
#include "speedRingMeterWidget/ttkspeedringmeterwidgetproperty.h"
#include "timeMeterWidget/ttktimemeterwidgetproperty.h"
#include "temperatureMeterWidget/ttktemperaturemeterwidgetproperty.h"

#endif // TTKMETERINCLUDE_H
