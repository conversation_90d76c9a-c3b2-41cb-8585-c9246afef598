# Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

name: doxygen

on: workflow_call

env:
  TTK_MODULE: TTKWidgetTools
  TTK_VERSTION: *******

jobs:
  build:
    name: Build on document by doxygen

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Build package
        run: |
          sudo apt install graphviz doxygen
          cd ${GITHUB_WORKSPACE}
          doxygen Doxyfile
          echo -n '<!DOCTYPE html>
          <html>
            <body>
              <script>window.location.href="html/index.html"</script>
            </body>
          </html>' > TTKDocs/main.html
          7z a ${{env.TTK_MODULE}}-${{env.TTK_VERSTION}}-document.7z TTKDocs
        working-directory: ${{runner.workspace}}

      - name: Archive artifacts
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{env.TTK_VERSTION}}
          token: ${{secrets.GITHUB_TOKEN}}
          generate_release_notes: false
          files: ${{runner.workspace}}/${{env.TTK_MODULE}}/${{env.TTK_MODULE}}-${{env.TTK_VERSTION}}-document.7z
