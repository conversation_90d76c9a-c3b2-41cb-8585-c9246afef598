#ifndef TTKNOT<PERSON><PERSON><PERSON><PERSON><PERSON>PROPERTY_H
#define TTKNOT<PERSON><PERSON>WI<PERSON><PERSON>PROPERTY_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include "ttkwidgetproperty.h"

/*!
* <AUTHOR> <<EMAIL>>
*/
class TTK_MODULE_EXPORT TTKNotifyWindowProperty : public TTKWidgetProperty
{
    Q_OBJECT
public:
    explicit TTKNotifyWindowProperty(QWidget *parent = nullptr);

private Q_SLOTS:
    virtual void intPropertyChanged(QtProperty *property, int value) override final;
    virtual void stringPropertyChanged(QtProperty *property, const QString &value) override final;
    virtual void pixmapPropertyChanged(QtProperty *property, const QString &value) override final;
    void showWidget();

private:
    QObject *m_manager;
    QString m_title, m_body, m_icon;

};

#endif // TTKNOTIFYWINDOWPROPERTY_H
