#ifndef TTKTOGGLEWIDGET_H
#define TTKTOGGLEWIDGET_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include <QAbstractButton>
#include "ttkmoduleexport.h"

class QState;
class QStateMachine;
class TTKToggleWidget;

/*!
 * <AUTHOR> <<EMAIL>>
 *   Heikki Johannes <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKToggleThumb : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(qreal shift WRITE setShift READ shift)
    Q_PROPERTY(QColor thumbColor WRITE setThumbColor READ thumbColor)
public:
    explicit TTKToggleThumb(TTKToggleWidget *parent = nullptr);

    void setShift(qreal shift);
    inline qreal shift() const { return m_shift; }

    void setThumbColor(const QColor &color);
    inline QColor thumbColor() const { return m_thumbColor; }

private:
    virtual bool eventFilter(QObject *obj, QEvent *event) override final;
    virtual void paintEvent(QPaintEvent *event) override final;

    void updateOffset();

    TTKToggleWidget *m_toggle;
    QColor m_thumbColor;
    qreal m_shift, m_offset;

};


/*!
 * <AUTHOR> <<EMAIL>>
 *   Heikki Johannes <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKToggleTrack : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QColor trackColor WRITE setTrackColor READ trackColor)
public:
    explicit TTKToggleTrack(TTKToggleWidget *parent = nullptr);

    void setTrackColor(const QColor &color);
    inline QColor trackColor() const { return m_trackColor; }

private:
    virtual bool eventFilter(QObject *obj, QEvent *event) override final;
    virtual void paintEvent(QPaintEvent *event) override final;

    TTKToggleWidget *m_toggle;
    QColor m_trackColor;

};


/*!
 * <AUTHOR> <<EMAIL>>
 *   Heikki Johannes <<EMAIL>>
 */
class TTK_MODULE_EXPORT TTKToggleWidget : public QAbstractButton
{
    Q_OBJECT
    TTK_DECLARE_MODULE(TTKToggleWidget)
public:
    explicit TTKToggleWidget(QWidget *parent = nullptr);
    ~TTKToggleWidget();

    void setDisabledColor(const QColor &color);
    QColor disabledColor() const;

    void setActiveColor(const QColor &color);
    QColor activeColor() const;

    void setInactiveColor(const QColor &color);
    QColor inactiveColor() const;

    void setTrackColor(const QColor &color);
    QColor trackColor() const;

    void setOrientation(Qt::Orientation orientation);
    Qt::Orientation orientation() const;

    virtual QSize sizeHint() const override final;

private:
    virtual void paintEvent(QPaintEvent *event) override final;

    void setupProperties();

    TTKToggleTrack *m_track;
    TTKToggleThumb *m_thumb;
    QStateMachine *m_stateMachine;
    QState *m_offState, *m_onState;
    Qt::Orientation m_orientation;
    QColor m_disabledColor, m_activeColor, m_inactiveColor, m_trackColor;

};

#endif // TTKTOGGLEWIDGET_H
