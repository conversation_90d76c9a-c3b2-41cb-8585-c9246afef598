QT LICENSE AGREEMENT
Agreement version 4.2.1

This Qt License Agreement ("Agreement") is a legal agreement for the licensing
of Licensed Software (as defined below) between The Qt Company (as defined
below) and the Licensee who has accepted the terms of this Agreement by
downloading or using the Licensed Software and/or as defined herein:

Capitalized terms used herein are defined in Section 1.

WHEREAS:
   (A)   Licensee wishes to use the Licensed Software for the purpose of
         developing and distributing Applications and/or Devices (each as
         defined below); and
   (B)   The Qt Company is willing to grant the Licensee a right to use
         Licensed Software for such a purpose pursuant to term and conditions
         of this Agreement.

NOW, THEREFORE, THE PARTIES HEREBY AGREE AS FOLLOWS:

1. DEFINITIONS
"Affiliate" of a Party shall mean an entity (i) which is directly or indirectly
controlling such Party; (ii) which is under the same direct or indirect
ownership or control as such Party; or (iii) which is directly or indirectly
owned or controlled by such Party.  For these purposes, an entity shall be
treated as being controlled by another if that other entity has fifty percent
(50 %) or more of the votes in such entity, is able to direct its affairs
and/or to control the composition of its board of directors or equivalent body.

"Add-on Products" shall mean The Qt Company's specific add-on software products
(for example Qt Safe Renderer, Qt for Automation, Qt Application Manager),
which are not licensed as part of The Qt Company's standard offering, but shall
be included into the scope of Licensed Software only if so specifically agreed
between the Parties.

"Applications" shall mean Licensee's software products created using the
Licensed Software, which may include the Redistributables, or part thereof.

"Contractor(s)" shall mean third party consultants, distributors and
contractors performing services to the Licensee under applicable contractual
arrangement.

"Customer(s)" shall mean Licensee's end users to whom Licensee, directly or
indirectly, distributes copies of the Redistributables.

"Data Protection Legislation" shall mean the General Data Protection Regulation
(EU 2016/679) (GDPR) and any national implementing laws, regulations and
secondary legislation, as may be amended or updated from time to time, as well
as any other data protection laws or regulations applicable in relevant
territory.

"Deployment Platforms" shall mean operating systems and/or hardware specified
in the License Certificate, on which the Redistributables can be distributed
pursuant to the terms and conditions of this Agreement.

"Designated User(s)" shall mean the employee(s) of Licensee or Licensee's
Affiliates acting within the scope of their employment or Licensee's
Contractors acting within the scope of their services for Licensee and on
behalf of Licensee. Designated Users shall be named in the License Certificate.

"Development License" shall mean the license needed by the Licensee for each
Designated User to use the Licensed Software under the license grant described
in Section 3.1 of this Agreement. Development Licenses are available
separately for Qt for Application Development and Qt for Device Creation
products, each product having its designated scope and purpose of use.
Distribution Licenses are always connected to Qt for Device Creation
product only.

"Development Platforms" shall mean those operating systems specified in the
License Certificate, in which the Licensed Software can be used under the
Development License, but not distributed in any form or used for any other
purpose.

"Devices" shall mean hardware devices or products that 1) are manufactured
and/or distributed by the Licensee or its Affiliates or Contractors, and
(2)(i) incorporate or integrate the Redistributables or parts thereof; or (ii)
where the main user interface or substantial functionality of such unit , when
used by a Customer, is provided by Application(s) or otherwise depends on the
Licensed Software, regardless of whether the Redistributables are distributed
together with the hardware or not. Devices covered with this Agreement shall
be specified in Appendix 2 or in a quote.

"Distribution License(s)" shall mean the license required for any kind of sale,
trade, exchange, loan, lease, rental or other distribution by or on behalf of
Licensee to a third party of Redistributables in connection with Devices
pursuant to license grant described in Section 3.3 of this Agreement.

"Distribution License Packs" shall mean set of prepaid Distribution Licenses
for distribution of Redistributables, as defined in The Qt Company's standard
price list, quote, Purchase Order confirmation or in an appendix hereto, as
the case may be.

"Intellectual Property Rights" shall mean patents (including utility models),
design patents, and designs (whether or not capable of registration), chip
topography rights and other like protection, copyrights, trademarks, service
marks, trade names, logos or other words or symbols and any other form of
statutory protection of any kind and applications for any of the foregoing as
well as any trade secrets.

"License Certificate" shall mean a certificate generated by The Qt Company for
each Designated User respectively upon them downloading the Licensed Software,
which will be available under respective Designated User's Qt Account at
account.qt.io. License Certificates will specify the Designated User, the
Development Platforms, Deployment Platforms and the License Term. Such terms
are considered part of the licenses granted hereunder and shall be updated
from time to time to reflect any agreed changes to the foregoing terms
relating to Designated User's rights to the Licensed Software.

"License Fee" shall mean the fee charged to the Licensee for rights granted
under the terms of this Agreement.

"License Term" shall mean the agreed validity period of the Development License
of the respective Designated User, during which time the Designated User is
entitled to use the Licensed Software, as set forth in the respective License
Certificate.

"Licensed Software" shall mean either
    (i)     Qt for Application Development or
    (ii)    Qt for Device Creation, and/or
    (iii)   Qt 3D Studio, and/or
    (iv)    Qt Design Studio, and/or
    (v)     Qt for MCUs, and/or
    (vi)    selected Add-on Products, if any,

depending on which product(s) the Licensee has purchased under this Agreement,
as well as corresponding online or electronic documentation, associated media
and printed materials, including the source code (where applicable), example
programs and the documentation, licensed to the Licensee under this Agreement.
Licensed Software does not include Third Party Software (as defined in Section
4) or Open Source Qt. The Qt Company may, in the course of its development
activities, at its free and absolute discretion and without any obligation to
send or publish any notifications to the Licensee or in general, make changes,
additions or deletions in the components and functionalities of the Licensed
Software, provided that no such changes, additions or deletions will affect
the already released version of the Licensed Software, but only upcoming
version(s).

"Licensee" shall mean the individual or legal entity that is party to this
Agreement, as identified on the signature page hereof.

"Licensee's Records" shall mean books and records that are likely to contain
information bearing on Licensee's compliance with this Agreement, Licensee's
use of Open Source Qt and/or the payments due to The Qt Company under this
Agreement, including, but not limited to user information, assembly logs,
sales records and distribution records.

"Modified Software" shall have the meaning as set forth in Section 2.3.

"Online Services" shall mean any services or access to systems made available
by The Qt Company to the Licensee over the Internet relating to the Licensed
Software or for the purpose of use by the Licensee of the Licensed Software or
Support. Use of any such Online Services is discretionary for the Licensee and
some of them may be subject to additional fees.

"Open Source Qt" shall mean the non-commercial Qt computer software products,
licensed under the terms of the GNU Lesser General Public License, version 2.1
or later ("LGPL") or the GNU General Public License, version 2.0 or later
("GPL"). For clarity, Open Source Qt shall not be provided nor governed under
this Agreement.

"Party" or "Parties" shall mean Licensee and/or The Qt Company.

"Permitted Combination" shall have the meaning as set forth in Section
3.4(viii).

"Pre-Release Code" shall have the meaning as set forth in Section 4.

"Prohibited Combination" shall mean any means to (i) use, combine, incorporate,
link or integrate Licensed Software with any software created with or
incorporating Open Source Qt, (ii) use Licensed Software for creation of any
software created with or incorporating Open Source Qt, or (iii) incorporate or
integrate Applications into a hardware device or product other than a Device.

"Qt 3D Studio" shall mean The Qt Company's productized offering, which consist
of all versions of modules and tools as set forth in Appendix 1.

"Qt Design Studio" shall mean The Qt Company's productized offering, which
consist of all versions of modules and tools as set forth in Appendix 1.

"Qt for Application Development" shall mean The Qt Company's productized
offering, which consist of all versions of modules and tools as set forth in
Appendix 1.

"Qt for Device Creation" shall mean The Qt Company's productized offering,
which consist of all versions of modules and tools as set forth in Appendix 1.

"Qt for MCUs" shall mean The Qt Company's productized offering, which consist
of all versions of modules and tools as set forth in Appendix 1.

"Redistributables" shall mean the portions of the Licensed Software set forth
in Appendix 1 that may be distributed pursuant to the terms of this Agreement
in object code form only, including any relevant documentation. Where
relevant, any reference to Licensed Software in this Agreement shall include
and refer also to Redistributables.

"Renewal Term" shall mean an extension of previous License Term as agreed
between the Parties.

"Submitted Modified Software" shall have the meaning as set forth in
Section 2.3.

"Support" shall mean standard developer support that is provided by The Qt
Company to assist Designated Users in using the Licensed Software in
accordance with The Qt Company's standard support terms available at
https://www.qt.io/terms-conditions/ and as further defined in Section 8
hereunder.

"Taxes" shall have the meaning set forth in Section 10.5.

"Term" shall have the meaning set forth in Section 12.

"The Qt Company" shall mean:
    (i)     in the event Licensee is an individual residing in the United
            States or a legal entity incorporated in the United States or
            having its headquarters in the United States, The Qt Company Inc.,
            a Delaware corporation with its office at 2350 Mission College
            Blvd., Suite 1020, Santa Clara, CA 95054, USA.; or
    (ii)    in the event the Licensee is an individual residing outside of the
            United States or a legal entity incorporated outside of the United
            States or having its registered office outside of the United
            States, The Qt Company Ltd., a Finnish company with its registered
            office at Bertel Jungin aukio D3A, 02600 Espoo, Finland.

"Third-Party Software" shall have the meaning set forth in Section 4.

"Updates" shall mean a release or version of the Licensed Software containing
bug fixes, error corrections and other changes that are generally made
available to users of the Licensed Software that have contracted for Support.
Updates are generally depicted as a change to the digits following the decimal
in the Licensed Software version number. The Qt Company shall make Updates
available to the Licensee under the Support. Updates shall be considered as
part of the Licensed Software hereunder.

"Upgrades" shall mean a release or version of the Licensed Software containing
enhancements and new features and are generally depicted as a change to the
first digit of the Licensed Software version number. In the event Upgrades are
provided to the Licensee under this Agreement, they shall be considered as
part of the Licensed Software hereunder.


2. OWNERSHIP
2.1. Ownership of The Qt Company
The Licensed Software is protected by copyright laws and international
copyright treaties, as well as other intellectual property laws and
treaties. The Licensed Software is licensed, not sold.

All of The Qt Company's Intellectual Property Rights are and shall remain the
exclusive property of The Qt Company or its licensors respectively.

2.2. Ownership of Licensee
All the Licensee's Intellectual Property Rights are and shall remain the
exclusive property of the Licensee or its licensors respectively.

All Intellectual Property Rights to the Modified Software, Applications and
Devices shall remain with the Licensee and no rights thereto shall be granted
by the Licensee to The Qt Company under this Agreement (except as set forth in
Section 2.3 below).

2.3. Modified Software
Licensee may create bug-fixes, error corrections, patches or modifications to
the Licensed Software ("Modified Software"). Such Modified Software may break
the source or binary compatibility with the Licensed Software (including
without limitation through changing the application programming interfaces
("API") or by adding, changing or deleting any variable, method, or class
signature in the Licensed Software and/or any inter-process protocols,
services or standards in the Licensed Software libraries). To the extent that
Licensee's Modified Software so breaks source or binary compatibility with the
Licensed Software, Licensee acknowledges that The Qt Company's ability to
provide Support may be prevented or limited and Licensee's ability to make use
of Updates may be restricted.

Licensee may, at its sole and absolute discretion, choose to submit Modified
Software to The Qt Company ("Submitted Modified Software") in connection with
Licensee's Support request, service request or otherwise. In the event
Licensee does so, then, Licensee hereby grants The Qt Company a sublicensable,
assignable, irrevocable, perpetual, worldwide, non-exclusive, royalty-free and
fully paid-up license, under all of Licensee's Intellectual Property Rights,
to reproduce, adapt, translate, modify, and prepare derivative works of,
publicly display, publicly perform, sublicense, make available and distribute
such Submitted Modified Software as The Qt Company sees fit at its free and
absolute discretion.

3. LICENSES GRANTED
3.1. Development with Licensed Software
Subject to the terms of this Agreement, The Qt Company grants to Licensee a
worldwide, non-exclusive, non-transferable license, valid for the License
Term, to use, modify and copy the Licensed Software by Designated Users on the
Development Platforms for the sole purposes of designing, developing,
demonstrating and testing Application(s) and/or Devices, and to provide
thereto related support and other related services to end-user Customers.

Licensee may install copies of the Licensed Software on five (5) computers per
Designated User, provided that only the Designated Users who have a valid
Development License may use the Licensed Software.

Licensee may at any time designate another Designated User to replace a
then-current Designated User by notifying The Qt Company in writing, provided
that any Designated User may be replaced only once during any six-month period.

Upon expiry of the initially agreed License Term, the respective License Terms
shall be automatically extended to one or more Renewal Term(s), unless and
until either Party notifies the other Party in writing that it does not wish
to continue the License Term, such notification to be provided to the other
Party no less than ninety (90) days before expiry of the respective License
Term. Unless otherwise agreed between the Parties, Renewal Term shall be of
equal length with the initial License Term.

Any such Renewal Term shall be subject to License Fees agreed between the
Parties or, if no advance agreement exists, subject to The Qt Company's
standard pricing applicable at the commencement date of any such Renewal Term.

Any price or other term specified for a Renewal Term shall be valid only for
the specified time.

The Qt Company may request the Licensee to place a purchase order corresponding
to a quote by The Qt Company for the relevant Renewal Term.

In the event Licensee does not prevent auto-renewal pursuant the above, but a
Renewal Term is nevertheless not duly ordered within 30 days from the date of
the respective quote from The Qt Company and/or the respective License Fee
paid by due date specified in The Qt Company's respective invoice, The Qt
Company shall apply a reinstatement fee equal to ten percent (10 %) of the
total value of the License Fees of the Development Licenses for the expired
term to be added to the License Fee of the respective Renewal Term.

In the event Licensee chooses not to renew a Development License for a Renewal
Term by notifying The Qt Company thereof no less than ninety (90) days before
expiry of the respective License Term, Licensee may still reinstate such
expired Development Licenses for a Renewal Term subject to applicable renewal
Term License Fees until thirty (30) days from the expiry of the initially
agreed License Term or preceding Renewal Term. After such thirty (30) day
period a Development License shall be subject to applicable License Fees for a
new Development License and not any Renewal Term License Fees.

3.2. Distribution of Applications
Subject to the terms of this Agreement, The Qt Company grants to Licensee a
worldwide, non-exclusive, non-transferable, revocable (for cause pursuant to
this Agreement) right and license, valid for the Term, to
    (i)     distribute, by itself or through its Contractors, Redistributables
            as installed, incorporated or integrated into Applications for
            execution on the Deployment Platforms, and
    (ii)    grant sublicenses to Redistributables, as distributed hereunder,
            for Customers solely for Customer's internal use and to the extent
            necessary in order for the Customers to use the Applications for
            their respective intended purposes.

Right to distribute the Redistributables as part of an Application as provided
herein is not royalty-bearing but is conditional upon the Licensee not having
any unpaid License Fees for Development Licenses owed to The Qt Company at the
time of distribution of any Redistributables to Customers.

3.3. Distribution of Devices
Subject to the terms of this Agreement, The Qt Company grants to Licensee a
worldwide, non-exclusive, non-transferable, revocable (for cause pursuant to
this Agreement) right and license, valid for the Term, to
    (i)     distribute, by itself or through one or more tiers of Contractors,
            Redistributables as installed, incorporated or integrated, or
            intended to be installed, incorporated or integrated into Devices
            for execution on the Deployment Platforms, and
    (ii)    grant sublicenses to Redistributables, as distributed hereunder,
            for Customers solely for Customer's internal use and to the extent
            necessary in order for the Customers to use the Devices for their
            respective intended purposes.

Right to distribute the Redistributables with Devices as provided herein is
conditional upon the Licensee (i) not having any unpaid License Fees for
Development Licenses owed to The Qt Company, and (ii) having purchased and
paid corresponding Distribution Licenses at the time of distribution of any
Redistributables to Customers.

3.4. Further Requirements
The licenses granted above in this Section 3 by The Qt Company to Licensee are
conditional and subject to Licensee's compliance with the following terms:
    (i)     Licensee shall not remove or alter any copyright, trademark or
            other proprietary rights notice(s) contained in any portion of the
            Licensed Software;
    (ii)    Applications must add primary and substantial functionality to the
            Licensed Software so as not to compete with the Licensed Software;
    (iii)   Applications may not pass on functionality which in any way makes
            it possible for others to create software with the Licensed
            Software; provided however that Licensee may use the Licensed
            Software's scripting and QML ("Qt Quick") functionality solely in
            order to enable scripting, themes and styles that augment the
            functionality and appearance of the Application(s) without adding
            primary and substantial functionality to the Application(s);
    (iv)    Licensee shall not use Licensed Software in any manner or for any
            purpose that infringes, misappropriates or otherwise violates any
            Intellectual property or right of any third party, or that
            violates any applicable law;
    (v)     Licensee shall not use The Qt Company's or any of its suppliers'
            names, logos, or trademarks to market Applications, except that
            Licensee may use "Built with Qt" logo to indicate that
            Application(s) was developed using the Licensed Software;
    (vi)    Licensee shall not distribute, sublicense or disclose source code
            of Licensed Software to any third party (provided however that
            Licensee may appoint employee(s) of Contractors as Designated
            Users to use Licensed Software pursuant to this Agreement). Such
            right may be available for the Licensee subject to a separate
            software development kit ("SDK") license agreement to be concluded
            with The Qt Company;
    (vii)   Licensee shall not grant the Customers a right to (i) make copies
            of the Redistributables except when and to the extent required to
            use the Applications and/or Devices for their intended purpose,
            (ii) modify the Redistributables or create derivative works
            thereof, (iii) decompile, disassemble or otherwise reverse
            engineer Redistributables, or (iv) redistribute any copy or
            portion of the Redistributables to any third party, except as part
            of the onward sale of the Device on which the Redistributables are
            installed;
    (viii)  Licensee shall not and shall cause that its Affiliates or
            Contractors shall not use Licensed Software in any Prohibited
            Combination, unless Licensee has received an advance written
            permission from The Qt Company to do so. Absent such written
            permission, any and all distribution by the Licensee during the
            Term of a hardware device or product a) which incorporate or
            integrate any part of Licensed Software or Open Source Qt; or b)
            where the main user interface or substantial functionality is
            provided by software built with Licensed Software or Open Source
            Qt or otherwise depends on the Licensed Software or Open Source
            Qt, shall be considered to be Device distribution under this
            Agreement and shall be dependent on Licensee's compliance thereof
            (including but not limited to obligation to pay applicable License
            Fees for such distribution). Notwithstanding what is provided
            above in this sub-section (viii), Licensee is entitled to use and
            combine Qt 3D Studio and/or Qt Design Studio with Open Source Qt
            ("Permitted Combination") for its internal evaluation purposes,
            provided that Licensee shall in no way transfer, publish, disclose,
            display or otherwise make available any software or work resulting
            from such Permitted Combination;
    (ix)    Licensee shall cause all of its Affiliates and Contractors
            entitled to make use of the licenses granted under this Agreement,
            to be contractually bound to comply with the relevant terms of
            this Agreement and not to use the Licensed Software beyond the
            terms hereof and for any purposes other than operating within the
            scope of their services for Licensee. Licensee shall be responsible
            for any and all actions and omissions of its Affiliates and
            Contractors relating to the Licensed Software and use thereof
            (including but not limited to payment of all applicable License
            Fees);
    (x)     Except when and to the extent explicitly provided in this Section
            3, Licensee shall not transfer, publish, disclose, display or
            otherwise make available the Licensed Software; and
    (xi)    Licensee shall not attempt or enlist a third party to conduct or
            attempt to conduct any of the above.

Above terms shall not be applicable if and to the extent they conflict with
any mandatory provisions of any applicable laws.
Any use of Licensed Software beyond the provisions of this Agreement is
strictly prohibited and requires an additional license from The Qt Company.

4. THIRD-PARTY SOFTWARE
The Licensed Software may provide links to third party libraries or code
(collectively "Third-Party Software") to implement various functions.
Third-Party Software does not comprise part of the Licensed Software. In some
cases, access to Third-Party Software may be included with the Licensed
Software. Such Third-Party Software will be listed in the ".../src/3rdparty"
source tree delivered with the Licensed Software or documented in the Licensed
Software, as such may be amended from time to time. Licensee acknowledges that
use or distribution of Third-Party Software is in all respects subject to
applicable license terms of applicable third-party right holders.

5. PRE-RELEASE CODE
The Licensed Software may contain pre-release code and functionality, or sample
code marked or otherwise stated with appropriate designation such as
"Technology Preview", "Alpha", "Beta", "Sample" etc. ("Pre-Release Code").

Such Pre-Release Code may be present complimentary for the Licensee, in order
to provide experimental support or information for new platforms or preliminary
versions of one or more new functionalities or for other similar reasons. The
Pre-Release Code may not be at the level of performance and compatibility of a
final, generally available, product offering. The Pre-Release Code may not
operate correctly, may contain errors and may be substantially modified by The
Qt Company prior to the first commercial product release, if any. The Qt
Company is under no obligation to make Pre-Release Code commercially available,
or provide any Support or Updates relating thereto. The Qt Company assumes no
liability whatsoever regarding any Pre-Release Code, but any use thereof is
exclusively at Licensee's own risk and expense.

For clarity, unless Licensed Software specifies different license terms for the
respective Pre-Release Code, the Licensee is entitled to use such pre-release
code pursuant to Section 3, just like other Licensed Software, provided however
that in the event Add-on Products are included and available as such
Pre-Release Code, Licensee's right to use such Add-on Products is nevertheless
subject to and conditional upon conclusion of separate agreement with The Qt
Company.

6. LIMITED WARRANTY AND WARRANTY DISCLAIMER
The Qt Company hereby represents and warrants that it has the power and
authority to grant the rights and licenses granted to Licensee under this
Agreement.

Except as set forth above, the Licensed Software is licensed to Licensee
"as is" and Licensee's exclusive remedy and The Qt Company's entire liability
for errors in the Licensed Software shall be limited, at The Qt Company's
option, to correction of the error, replacement of the Licensed Software or
return of the applicable fees paid for the defective Licensed Software for the
time period during which the License is not able to utilize the Licensed
Software under the terms of this Agreement.

TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE QT COMPANY ON BEHALF OF
ITSELF AND ITS LICENSORS, SUPPLIERS AND AFFILIATES, DISCLAIMS ALL OTHER
WARRANTIES, EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, ANY IMPLIED
WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND
NON-INFRINGEMENT WITH REGARD TO THE LICENSED SOFTWARE. THE QT COMPANY DOES NOT
WARRANT THAT THE LICENSED SOFTWARE WILL SATISFY LICENSEE'S REQUIREMENTS OR THAT
IT WILL OPERATE WITHOUT DEFECT OR ERROR OR THAT THE OPERATION THEREOF WILL BE
UNINTERRUPTED.

7. INDEMNIFICATION AND LIMITATION OF LIABILITY
7.1. Limitation of Liability
EXCEPT FOR (I) CASES OF GROSS NEGLIGENCE OR INTENTIONAL MISCONDUCT,  AND (II)
BREACH OF CONFIDENTIALITY, AND TO THE EXTENT PERMITTED BY APPLICABLE LAW, IN NO
EVENT SHALL EITHER PARTY BE LIABLE TO THE OTHER PARTY FOR ANY LOSS OF PROFIT,
LOSS OF DATA, LOSS OF BUSINESS OR GOODWILL OR ANY OTHER INDIRECT, SPECIAL,
CONSEQUENTIAL, INCIDENTAL OR PUNITIVE COST, DAMAGES OR EXPENSE OF ANY KIND,
HOWSOEVER ARISING UNDER OR IN CONNECTION WITH THIS AGREEMENT.

EXCEPT FOR (I) CASES OF GROSS NEGLIGENCE OR INTENTIONAL MISCONDUCT,  AND (II)
BREACH OF CONFIDENTIALITY, AND TO THE EXTENT PERMITTED BY APPLICABLE LAW, IN NO
EVENT SHALL EITHER PARTY'S TOTAL AGGREGATE LIABILITY UNDER THIS AGREEMENT
EXCEED THE AGGREGATE LICENSE FEES PAID OR PAYABLE TO THE QT COMPANY FROM
LICENSEE DURING THE PERIOD OF TWELVE (12) MONTHS IMMEDIATELY PRECEDING THE
EVENT RESULTING IN SUCH LIABILITY.

THE PROVISIONS OF THIS SECTION 7 ALLOCATE THE RISKS UNDER THIS AGREEMENT
BETWEEN THE QT COMPANY AND LICENSEE AND THE PARTIES HAVE RELIED UPON THE
LIMITATIONS SET FORTH HEREIN IN DETERMINING WHETHER TO ENTER INTO THIS
AGREEMENT.

NOTWITHSTANDING ANYTHING TO THE CONTRARY IN THIS AGREEMENT, LICENSEE SHALL
ALWAYS BE LIABLE TO PAY THE APPLICABLE LICENSE FEES CORRESPONDING TO ITS ACTUAL
USE OF LICENSED SOFTWARE.

8. SUPPORT, UPDATES AND ONLINE SERVICES
Upon due payment of the agreed License Fees the Licensee will be eligible to
receive Support and Updates and to use the Online Services during the License
Term, provided, however, that in the event the License Term is longer than 36
months, the initial payment includes Support for only the first 12 months,
unless the Parties specifically otherwise agree.

Unless otherwise decided by The Company at its free and absolute discretion,
Upgrades will not be included in the Support but may be available subject to
additional fees.

From time to time The Qt Company may change the Support terms, provided that
during the respective ongoing License Term the level of Support provided by The
Qt Company may not be reduced without the consent of the Licensee.

Unless otherwise agreed, The Qt Company shall not be responsible for providing
any service or support to Customers.

9. CONFIDENTIALITY
Each Party acknowledges that during the Term of this Agreement each Party may
receive information about the other Party's business, business methods,
business plans, customers, business relations, technology, and other
information, including the terms of this Agreement, that is confidential and of
great value to the other Party, and the value of which would be significantly
reduced if disclosed to third parties ("Confidential Information").
Accordingly, when a Party (the "Receiving Party") receives Confidential
Information from the other Party (the "Disclosing Party"), the Receiving Party
shall only disclose such information to employees and Contractors on a need to
know basis, and shall cause its employees and employees of its Affiliates to:
(i) maintain any and all Confidential Information in confidence; (ii) not
disclose the Confidential Information to a third party without the Disclosing
Party's prior written approval; and (iii) not, directly or indirectly, use the
Confidential Information for any purpose other than for exercising its rights
and fulfilling its responsibilities pursuant to this Agreement. Each Party
shall take reasonable measures to protect the Confidential Information of the
other Party, which measures shall not be less than the measures taken by such
Party to protect its own confidential and proprietary information.

Obligation of confidentiality shall not apply to information that (i) is or
becomes generally known to the public through no act or omission of the
Receiving Party; (ii) was in the Receiving Party's lawful possession prior to
the disclosure hereunder and was not subject to limitations on disclosure or
use; (iii) is developed independently by employees or Contractors of the
Receiving Party or other persons working for the Receiving Party who have not
had access to the Confidential Information of the Disclosing Party, as proven
by the written records of the Receiving Party; (iv) is lawfully disclosed to
the Receiving Party without restrictions, by a third party not under an
obligation of confidentiality; or (v) the Receiving Party is legally compelled
to disclose, in which case the Receiving Party shall notify the Disclosing
Party of such compelled disclosure and assert the privileged and confidential
nature of the information and cooperate fully with the Disclosing Party to
limit the scope of disclosure and the dissemination of disclosed Confidential
Information to the minimum extent necessary.

The obligations under this Section 9 shall continue to remain in force for a
period of five (5) years after the last disclosure, and, with respect to trade
secrets, for so long as such trade secrets are protected under applicable trade
secret laws.

10. FEES, DELIVERY AND PAYMENT
10.1. License Fees
License Fees are described in The Qt Company's standard price list, quote or
Purchase Order confirmation or in an appendix hereto, as the case may be.

The License Fees shall not be refunded or claimed as a credit in any event or
for any reason whatsoever.

10.2. Ordering Licenses
Licensee may purchase Development Licenses and Distribution Licenses pursuant
to agreed pricing terms or, if no specific pricing terms have been agreed upon,
at The Qt Company's standard pricing terms applicable at the time of purchase.
Unless specifically otherwise provided, any pricing terms referenced in this
Agreement shall be valid for twelve (12) months from the date of this Agreement.

Licensee shall submit all purchase orders for Development Licenses and
Distribution Licenses to The Qt Company by email or any other method acceptable
to The Qt Company (each such order is referred to herein as a "Purchase Order")
for confirmation, whereupon the Purchase Order shall become binding between the
Parties.

10.3. Distribution License Packs
Unless otherwise agreed, Distribution Licenses shall be purchased by way of
Distribution License Packs.

Upon due payment of the ordered Distribution License Pack(s), the Licensee will
have an account of Distribution Licenses available for distributing the
Redistributables in accordance with this Agreement.

Each time Licensee distributes a copy of Redistributables, then one
Distribution License is used, and Licensee's account of available Distribution
Licenses is decreased accordingly.Licensee may distribute copies of the
Redistributables so long as Licensee has Distribution Licenses remaining on
its account.

10.4. Payment Terms
License Fees and any other charges under this Agreement shall be paid by
Licensee no later than thirty (30) days from the date of the applicable
invoice from The Qt Company.

The Qt Company will submit an invoice to Licensee after the date of this
Agreement and/or after The Qt Company receives a Purchase Order from Licensee.

A late payment charge of the lower of (a) one percent per month; or (b) the
interest rate stipulated by applicable law, shall be charged on any unpaid
balances that remain past due.

10.5. Taxes
All License Fees and other charges payable hereunder are gross amounts but
exclusive of any value added tax, use tax, sales tax, withholding tax and other
taxes, duties or tariffs ("Taxes") levied directly for the sale, delivery or
use of Licensed Software hereunder pursuant to any applicable law. Such
applicable Taxes shall be paid by Licensee to The Qt Company, or, where
applicable, in lieu of payment of such Taxes to The Qt Company, Licensee shall
provide an exemption certificate to The Qt Company and any applicable authority.

11. RECORD-KEEPING AND REPORTING OBLIGATIONS; AUDIT RIGHTS
11.1. Licensee's Record-keeping
Licensee shall at all times during the Term of this Agreement and for a period
of seven (7) years thereafter maintain Licensee's Records in an accurate and
up-to-date form. Licensee's Records shall be adequate to reasonably enable The
Qt Company to determine Licensee's compliance with the provisions of this
Agreement. The records shall conform to general good accounting practices.

Licensee shall, within thirty (30) days from receiving The Qt Company's request
to that effect, deliver to The Qt Company a report based on Licensee's Records,
such report to contain information, in sufficient detail, on (i) number and
identity of users working with Licensed Software or Open Source Qt, (ii) copies
of Redistributables distributed by Licensee during the most recent calendar
quarter and/or any other term specified by The Qt Company, (iii) number of
undistributed copies of Redistributables and corresponding number of unused
Distribution Licenses remaining on Licensee's account, and (iv) any other
information as The Qt Company may reasonably require from time to time.

11.2. The Qt Company's Audit Rights
The Qt Company or an independent auditor acting on behalf of The Qt Company's,
may, upon at least five (5) business days' prior written notice and at its
expense, audit Licensee with respect to the Licensee's use of the Licensed
Software, but not more frequently than once during each 6-month period. Such
audit may be conducted by mail, electronic means or through an in-person visit
to Licensee's place of business. Any such in-person audit shall be conducted
during regular business hours at Licensee's facilities and shall not
unreasonably interfere with Licensee's business activities. The Qt Company or
the independent auditor acting on behalf of The Qt Company shall be entitled to
inspect Licensee's Records and conduct necessary interviews of Licensee's
relevant employees and Contractors. All such Licensee's Records and use thereof
shall be subject to an obligation of confidentiality under this Agreement.

If an audit reveals that Licensee is using the Licensed Software beyond scope
of the licenses Licensee has paid for, Licensee agrees to pay The Qt Company
any amounts owed for such unauthorized use within 30 days from receipt of the
corresponding invoice from The Qt Company. In addition, in the event the audit
reveals a material violation of the terms of this Agreement (without
limitation, either (i) underpayment of more than 10 % of License Fees or 10,000
euros (whichever is more) or (ii) distribution of products, which include or
result from Prohibited Combination, shall be deemed a material violation for
purposes of this section), then the Licensee shall pay The Qt Company's
reasonable cost of conducting such audit.

12. TERM AND TERMINATION
12.1. Agreement Term
This Agreement shall enter into force upon due acceptance by both Parties and
remain in force for as long as there is any Development License(s) purchased
under this Agreement in force ("Term"), unless and until terminated pursuant to
the terms of this Section 12.

12.2. Termination and suspension of rights
Either Party shall have the right to terminate this Agreement upon thirty (30)
days prior written notice if the other Party commits a material breach of any
obligation of this Agreement and fails to remedy such breach within such notice
period.

Instead of termination, The Qt Company shall have the right to suspend or
withhold grants of all rights to the Licensed Software hereunder, including but
not limited to the Development Licenses, Distribution License, and Support,
should Licensee fail to make payment in timely fashion or otherwise violates or
is reasonably suspected to violate its obligations or terms of this Agreement,
and where such violation or breach is not cured within five (5) business days
following The Qt Company's written notice thereof.

12.3. Mutual Right to Terminate
Either Party shall have the right to terminate this Agreement immediately upon
written notice in the event that the other Party becomes insolvent, files for
any form of bankruptcy, makes any assignment for the benefit of creditors, has
a receiver, administrative receiver or officer appointed over the whole or a
substantial part of its assets, ceases to conduct business, or an act
equivalent to any of the above occurs under the laws of the jurisdiction of the
other Party.

12.4. Parties´ Rights and Duties upon Termination
Upon expiry or termination of the Agreement, Licensee shall cease and shall
cause all Designated Users (including those of its Affiliates' and
Contractors') to cease using the Licensed Software and distribution of the
Redistributables under this Agreement.

Notwithstanding the above, in the event the Agreement expires or is terminated:
    (i)     as a result of The Qt Company choosing not to renew the Development
            License(s) as set forth in Section 3.1, then all valid licenses
            possessed by the Licensee at such date shall be extended to be
            valid in perpetuity under the terms of this Agreement and Licensee
            is entitled to purchase additional licenses as set forth in
            Section 10.2; or
    (ii)    for reason other than by The Qt Company pursuant to item (i) above
            or pursuant to Section 12.2, then the Licensee is entitled, for a
            period of six (6) months after the effective date of termination,
            to continue distribution of Devices under the Distribution Licenses
            paid but unused at such effective date of termination.

Upon any such termination the Licensee shall destroy or return to The Qt
Company all copies of the Licensed Software and all related materials and will
certify the same to The Qt Company upon its request, provided however that
Licensee may retain and exploit such copies of the Licensed Software as it may
reasonably require in providing continued support to Customers.

Expiry or termination of this Agreement for any reason whatsoever shall not
relieve Licensee of its obligation to pay any License Fees accrued or payable
to The Qt Company prior to the effective date of termination, and Licensee
shall immediately pay to The Qt Company all such fees upon the effective date
of termination. Termination of this Agreement shall not affect any rights of
Customers to continue use of Applications and Devices (and therein incorporated
Redistributables).

12.5. Extension in case of bankruptcy
In the event The Qt Company is declared bankrupt under a final, non-cancellable
decision by relevant court of law, and this Agreement is not, at the date of
expiry of the Development License(s) pursuant to Section 3.1, assigned to
party, who has assumed The Qt Company's position as a legitimate licensor of
Licensed Software under this Agreement, then all valid licenses possessed by
the Licensee at such date of expiry, and which the Licensee has not notified
for expiry, shall be extended to be valid in perpetuity under the terms of this
Agreement.

13. GOVERNING LAW AND LEGAL VENUE
In the event this Agreement is in the name of The Qt Company Inc., a Delaware
Corporation, then:
    (i)     this Agreement shall be construed and interpreted in accordance
            with the laws of the State of California, USA, excluding its choice
            of law provisions;
    (ii)    the United Nations Convention on Contracts for the International
            Sale of Goods will not apply to this Agreement; and
    (iii)   any dispute, claim or controversy arising out of or relating to
            this Agreement or the breach, termination, enforcement,
            interpretation or validity thereof, including the determination of
            the scope or applicability of this Agreement to arbitrate, shall
            be determined by arbitration in San Francisco, USA, before one
            arbitrator. The arbitration shall be administered by JAMS pursuant
            to JAMS' Streamlined Arbitration Rules and Procedures. Judgment on
            the Award may be entered in any court having jurisdiction. This
            Section shall not preclude parties from seeking provisional
            remedies in aid of arbitration from a court of appropriate
            jurisdiction.

In the event this Agreement is in the name of The Qt Company Ltd., a Finnish
Company, then:
    (i)     this Agreement shall be construed and interpreted in accordance
            with the laws of Finland, excluding its choice of law provisions;
    (ii)    the United Nations Convention on Contracts for the International
            Sale of Goods will not apply to this Agreement; and
    (iii)   any disputes, controversy or claim arising out of or relating to
            this Agreement, or the breach, termination or validity thereof
            shall be finally settled by arbitration in accordance with the
            Arbitration Rules of Finland Chamber of Commerce. The arbitration
            tribunal shall consist of one (1), or if either Party so requires,
            of three (3), arbitrators. The award shall be final and binding and
            enforceable in any court of competent jurisdiction. The arbitration
            shall be held in Helsinki, Finland and the process shall be
            conducted in the English language. This Section shall not preclude
            parties from seeking provisional remedies in aid of arbitration
            from a court of appropriate jurisdiction.

14. GENERAL PROVISIONS
14.1. No Assignment
Except in the case of a merger or sale of substantially all of its corporate
assets, Licensee shall not be entitled to assign or transfer all or any of its
rights, benefits and obligations under this Agreement without the prior written
consent of The Qt Company, which shall not be unreasonably withheld or delayed.
The Qt Company shall be entitled to freely assign or transfer any of its
rights, benefits or obligations under this Agreement.

14.2. No Third-Party Representations
Licensee shall make no representations or warranties concerning the Licensed
Software on behalf of The Qt Company. Any representation or warranty Licensee
makes or purports to make on The Qt Company's behalf shall be void as to The
Qt Company.

14.3. Surviving Sections
Any terms and conditions that by their nature or otherwise reasonably should
survive termination of this Agreement shall so be deemed to survive. Such
sections include especially the following: 1, 2, 6, 7, 9, 11, 12.4, 13 and 14.

14.4. Entire Agreement
This Agreement, the exhibits hereto, the License Certificate and any applicable
Purchase Order accepted by The Qt Company constitute the complete agreement
between the Parties and supersedes all prior or contemporaneous discussions,
representations, and proposals, written or oral, with respect to the subject
matters discussed herein.

In the event of any conflict or inconsistency between this Agreement and any
Purchase Order, the terms of this Agreement will prevail over the terms of the
Purchase Order with respect to such conflict or inconsistency.

Parties specifically acknowledge and agree that this Agreement prevails over
any click-to-accept or similar agreements the Designated Users may need to
accept online upon download of the Licensed Software, as may be required by
The Qt Company's applicable processes relating to Licensed Software.

14.5. Modifications
No modification of this Agreement shall be effective unless contained in a
writing executed by an authorized representative of each Party. No term or
condition contained in Licensee's Purchase Order ("Deviating Terms") shall
apply unless The Qt Company has expressly agreed such Deviating Terms in
writing. Unless and to the extent expressly agreed by The Qt Company, any such
Deviating Terms shall be deemed void and with no legal effect. For clarity,
delivery of the Licensed Software following the receipt of the Purchase Order
including Deviating Terms shall not constitute acceptance of such Deviating
Terms."

14.6. Force Majeure
Except for the payment obligations hereunder, neither Party shall be liable to
the other for any delay or non-performance of its obligations hereunder in the
event and to the extent that such delay or non-performance is due to an event
of act of God, terrorist attack or other similar unforeseeable catastrophic
event that prevents either Party for fulfilling its obligations under this
Agreement and which such Party cannot avoid or circumvent ("Force Majeure
Event"). If the Force Majeure Event results in a delay or non-performance of a
Party for a period of three (3) months or longer, then either Party shall have
the right to terminate this Agreement with immediate effect without any
liability (except for the obligations of payment arising prior to the event of
Force Majeure) towards the other Party.

14.7. Notices
Any notice given by one Party to the other shall be deemed properly given and
deemed received if specifically acknowledged by the receiving Party in writing
or when successfully delivered to the recipient by hand, fax, or special
courier during normal business hours on a business day to the addresses
specified for each Party on the signature page. Each communication and document
made or delivered by one Party to the other Party pursuant to this Agreement
shall be in the English language.

14.8. Export Control
Licensee acknowledges that the Redistributables, as incorporated in
Applications or Devices, may be subject to export control restrictions under
the applicable laws of respective countries. Licensee shall fully comply with
all applicable export license restrictions and requirements as well as with all
laws and regulations relating to the Redistributables and exercise of licenses
hereunder and shall procure all necessary governmental authorizations,
including without limitation, all necessary licenses, approvals, permissions or
consents, where necessary for the re-exportation of the Redistributables,
Applications and/or Devices.

14.9. No Implied License
There are no implied licenses or other implied rights granted under this
Agreement, and all rights, save for those expressly granted hereunder, shall
remain with The Qt Company and its licensors. In addition, no licenses or
immunities are granted to the combination of the Licensed Software with any
other software or hardware not delivered by The Qt Company under this Agreement.

14.10. Attorney Fees
The prevailing Party in any action to enforce this Agreement shall be entitled
to recover its attorney's fees and costs in connection with such action.

14.11. Privacy
Licensee acknowledges and agrees that for the purpose of this Agreement, The Qt
Company may collect, use, transfer and disclose personal data pertaining to
Designated Users as well as any other employees and directors of the Licensee
and its Contractors relevant for carrying out the intent of this Agreement.
Such personal data may be collected from the Licensee or directly from the
relevant individuals. The Parties acknowledge that with regard to such personal
data processed hereunder, The Qt Company shall be regarded as the Data
Controller under the applicable Data Protection Legislation. The Qt Company
shall process any such personal data in accordance with its privacy policies
and practices, which will comply with all applicable requirements of the Data
Protection Legislation.

14.12. Severability
If any provision of this Agreement shall be adjudged by any court of competent
jurisdiction to be unenforceable or invalid, that provision shall be limited or
eliminated to the minimum extent necessary so that this Agreement shall
otherwise remain in full force and effect and enforceable.



APPENDICES
The Agreement includes Appendix 1, and possibly one or more of the appendices
3-5, depending on the product(s) purchased by the Licensee, what is stated in
the quote or invoice, and/or what is stated in the Licensee's License
Certificate.


APPENDIX 1
The modules and/or tools that are included in the respective product - Qt for
Application Development (QtAD), Qt for Device Creation (QtDC), Qt for MCUs
(QtMCU), Qt 3D Studio (Qt3DS) and Qt Design Studio (QtDS) - are marked with 'X'
in the below table.

Parts of the product that are permitted for distribution in object-code form
only ("Redistributables") are marked with 'R' in the below table.

Modules/Tools                          | QtAD  | QtDC  | QtMCU | Qt3DS | QtDS
-------------------------------------------------------------------------------
Qt Core                                |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt GUI                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Multimedia                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Multimedia Widgets                  |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Network                             |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt QML                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick                               |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Controls 2                    |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Dialogs                       |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Layouts                       |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Test                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt SQL                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Test                                |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Widgets                             |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Active Qt                              |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt 3D                                  |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Android Extras                      |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Bluetooth                           |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Canvas 3D                           |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Concurrent                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt D-Bus                               |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Gamepad                             |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Graphical Effects                   |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Help                                |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Image Formats                       |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Location                            |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Mac Extras                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Network Authorization               |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt NFC                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Platform Headers                    |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Positioning                         |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Print Support                       |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Purchasing                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt for Python                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Controls                      |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Extras                        |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Widgets                       |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt SCXML                               |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Sensors                             |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Serial Bus                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Serial Port                         |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Speech                              |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt SVG                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt UI Tools                            |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt WebChannel                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt WebEngine                           |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt WebSockets                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt WebView                             |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Windows Extras                      |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt X11 Extras                          |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt XML                                 |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt XML Patterns                        |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Wayland Compositor                  |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Charts                              |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Data Visualization                  |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Virtual Keyboard                    |  X,R  |  X,R  |       |       |
-------------------------------------------------------------------------------
Boot 2 Qt stack                        |       |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt OTA                                 |       |  X,R  |       |       |
-------------------------------------------------------------------------------
Device Utilities                       |       |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Debugging Bridge (QDB) Daemon       |       |  X,R  |       |       |
-------------------------------------------------------------------------------
Qt Quick Ultralite Controls            |       |       |  X,R  |       |
-------------------------------------------------------------------------------
Qt Quick Ultralite                     |       |       |  X,R  |       |
-------------------------------------------------------------------------------
Qt Creator                             |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
Qt Designer (Qt Widget Designer)       |   X   |   X   |       |       |
-------------------------------------------------------------------------------
Qt Quick Designer (Qt Creator plugin)  |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
Qt Linguist                            |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
Qt Assistant                           |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
lupdate                                |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
lrelease                               |   X   |   X   |   X   |       |
-------------------------------------------------------------------------------
qmake                                  |   X   |   X   |       |       |
-------------------------------------------------------------------------------
uic                                    |   X   |   X   |       |       |
-------------------------------------------------------------------------------
rcc                                    |   X   |   X   |       |       |
-------------------------------------------------------------------------------
qlalr                                  |   X   |   X   |       |       |
-------------------------------------------------------------------------------
qdoc                                   |   X   |   X   |       |       |
-------------------------------------------------------------------------------
qmlscene                               |   X   |   X   |       |       |
-------------------------------------------------------------------------------
qmlviewer                              |   X   |   X   |       |       |
-------------------------------------------------------------------------------
Target toolchains                      |       |   X   |   X   |       |
-------------------------------------------------------------------------------
Qt Debugging Bridge (QDB) Host Tools   |       |   X   |       |       |
-------------------------------------------------------------------------------
qtconfig-gui                           |       |   X   |       |       |
-------------------------------------------------------------------------------
Qt Emulator                            |       |   X   |       |       |
-------------------------------------------------------------------------------
qmlinterfacegenerator                  |       |       |   X   |       |
-------------------------------------------------------------------------------
qmltocpp                               |       |       |   X   |       |
-------------------------------------------------------------------------------
qulfontcompiler                        |       |       |   X   |       |
-------------------------------------------------------------------------------
Qt53DStudioRuntime2                    |       |       |       |  X,R  |
-------------------------------------------------------------------------------
Qt 3D Studio                           |       |       |       |   X   |
-------------------------------------------------------------------------------
Qt Design Studio                       |       |       |       |       |   X
-------------------------------------------------------------------------------


APPENDIX 3: ADDITIONS TO LICENSED SOFTWARE
In addition to what is provided under the definition of the Licensed Software,
Parties agree that Licensed Software shall also include the Add-On Products of
The Qt Company, as mentioned in this Appendix, if included in the
quote / invoice.

The Modules and/or Tools of the Licensed Software that are included with each
Add-On Product respectively are marked with 'X' in the below table. Parts of
the respective Add-On Product that are permitted for distribution in
object-code form only ("Redistributables") are marked with 'R' in the below
table.

-------------------------------------------------------------------------------
            |                          Add-On Product(s)
Modules /   |------------------------------------------------------------------
Tools of    |Qt for     |Qt         |Qt Safe  |Qt         |Qt       |Qt
Licensed    |Automation |Automotive |Renderer |Application|Gammaray |Deployment
Software    |           |Suite      |         |Manager    |         |Platform
            |           |           |         |           |         |Package
-------------------------------------------------------------------------------
Qt MQTT     |   X,R     |           |         |           |         |
-------------------------------------------------------------------------------
Qt KNX      |   X,R     |           |         |           |         |
-------------------------------------------------------------------------------
Qt OPC UA   |   X,R     |           |         |           |         |
-------------------------------------------------------------------------------
Qt CoAP     |   X,R     |           |         |           |         |
-------------------------------------------------------------------------------
Qt Safe     |           |    X,R    |   X,R   |           |         |
Renderer    |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt          |           |           |         |           |         |
Application |           |    X,R    |         |    X,R    |         |
Manager     |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt IVI      |           |    X,R    |         |           |         |
-------------------------------------------------------------------------------
Reference UI|           |    X,R    |         |           |         |
-------------------------------------------------------------------------------
Qt GENIVI   |           |    X,R    |         |           |         |
Extras      |           |           |         |           |         |
-------------------------------------------------------------------------------
QML Live    |           |     X     |         |           |         |
-------------------------------------------------------------------------------
Qt Creator  |           |     X     |         |           |         |
Deployment  |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt Creator  |           |           |         |           |         |
Plugin for  |           |           |         |           |         |
Qt          |           |     X     |         |     X     |         |
Application |           |           |         |           |         |
Manager     |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt          |           |           |         |           |         |
Automotive  |           |           |         |           |         |
Suite       |           |     X     |         |           |         |
Deployment  |           |           |         |           |         |
Server      |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt Design   |           |     X     |         |           |         |
Studio      |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt 3D Studio|           |     X     |         |           |         |
-------------------------------------------------------------------------------
GammaRay    |           |     X     |         |           |    X    |
-------------------------------------------------------------------------------
Platform    |           |           |         |           |         |
adaptations |           |           |         |           |         |
for         |           |           |         |           |         |    X
specified   |           |           |         |           |         |
Deployment  |           |           |         |           |         |
Platforms   |           |           |         |           |         |
-------------------------------------------------------------------------------
Qt for      |           |           |         |           |         |
Device      |           |     X     |         |           |         |
Creation    |           |           |         |           |         |
-------------------------------------------------------------------------------

All the above Redistributables are subject to applicable provisions and
limitations including but not limited to what is defined in section 3 of the
Agreement.


APPENDIX 4: SMALL BUSINESS AND STARTUP
The provisions of this Appendix 4 are applicable for Start-up Companies and for
the Evaluation Term.For the purpose of this Appendix 4, the following
additional definitions shall be applicable:

"Trial Term" shall mean a period of twelve (12) months.

"Start-up Company" means a company with a maximum annual revenue, including
funding, equivalent to 100,000 USD (in applicable currency) during a respective
calendar year, as evidenced by duly audited records of the Licensee and
approved by The Qt Company.

During the Trial Term, Section 3 shall apply with following modifications
("Trial Term Modifications"):
    (i)     Licenses granted under Sections 3.1 and 3.2 shall be free of any
            charge. For clarity, License for distribution of Devices pursuant
            to Section 3.3 is subject to applicable License Fee for necessary
            Distribution Licenses;
    (ii)    Development License under Section 3.1 is limited to a maximum of
            three (3) Designated Users; and
    (iii)   Support is available subject to availability, as judged by The Qt
            Company at its free and absolute discretion, provided that support
            will be limited to a maximum of ten (10) tickets during the Trial
            Term.

Upon expiry of the Trial Term:
    (a)     This Appendix 4 is terminated, Trial Term Modifications cease to
            remain in force, Licensee's Development Licenses shall be
            automatically converted into licenses subject to a License Fee (in
            the amount specified in the quote or in Appendix 2 and payable with
            a 30-day payment term) and Licensee's rights and obligations under
            this Agreement shall continue to remain in force under the standard
            provisions of the Agreement, unless the Licensee notifies The Qt
            Company in writing no less than ninety (90) days before such expiry
            date that Licensee does not agree to such continuance, in which
            event the Agreement, and all rights of the Licensee thereunder,
            shall expire; provided however that
    (b)     in the event the Licensee still qualifies as a Start-up Company,
            the Licensee has an option ("Option"), instead of what is stated in
            item a) above, to renew the Trial Term. Renewal is limited to one
            time, and the total duration of Trial Term is thus 24 months after
            the effective date. Licensee shall notify The Qt Company in
            writing, no less than ninety (90) days before the expiry date, if
            Licensee wish to exercise the Option.


APPENDIX 5: NON-COMMERCIAL USE
The provisions of this Appendix 5 are applicable for non-commercial use of the
Licensed Software by the Licensee.

For the purpose of this Appendix 5, the following additional definitions
(replacing the relevant definition of the Agreement, where applicable) shall be
applicable:

"Demo Units" shall mean (i) hardware development platform, which incorporates
the Licensed Software along with Licensee's software and/or hardware, and (ii)
prototype versions of Applications or Devices.

"Designated User(s)" shall mean the employees and students of the Licensee.

"Licensee Products" shall mean Applications and/or Devices.

"Permitted Purpose" shall mean (i) Licensee's internal evaluation and testing
of Licensed Software, (ii) building Demo Units as well as (iii) educational
use.

"Term" shall mean a period of twelve (12) months or any such other period as
may be agreed between the Parties.

For the purpose of this Appendix 5, the following changes shall be agreed with
respect to relevant Sections of the Agreement:
    I.      Recital (A) shall be replaced in its entirety to read as follows:

            "(A) Licensee wishes to use the Licensed Software for the Permitted
            Purpose."

    II.     Section 3.1 shall be replaced in its entirety to read as follows:

            "The Qt Company grants to Licensee a personal, non-exclusive,
            non-transferable, revocable, royalty-free license, valid for the
            Term, to use, modify and copy the Licensed Software solely for the
            Permitted Purpose.

            Licensee may install copies of the Licensed Software on an
            unlimited number of computers provided that only Designated Users
            may use the Licensed Software.

            Licensee may demonstrate the Demo Units, provided that such
            demonstrations must be conducted by Licensee, and the Demo Units
            must remain in Licensee's possession and under Licensee's control
            at all times.

            For clarity, this Agreement does not (i) entitle Licensee to use
            Licensed Software to create Applications or Devices (other than
            prototypes thereof) or (ii) carry any distribution rights to
            Licensee, but such rights are subject to and conditional upon
            conclusion of a separate license agreement with The Qt Company."

    III.    Sections 3.2, 3.3, 8 and 10 shall be deleted.

    IV.     Section 3.4 shall be replaced in its entirety to read as follows:

            "Licensee shall not:
                - remove or alter any copyright, trademark or other proprietary
                  rights notice contained in any portion of the Licensed
                  Software;

                - transfer, publish, sublicense, disclose, display or otherwise
                  make the Licensed Software available to any third party
                  (except that Licensee may demonstrate the Demo Units pursuant
                  to Section 3.1);

                - in any way combine, incorporate or integrate Licensed
                  Software with, or use Licensed Software for creation of, any
                  software created with or incorporating Open Source Qt;

            Licensee shall cause all Designated Users who make use of the
            licenses granted under this Agreement, to be contractually bound to
            comply with the relevant terms of this Agreement and not to use the
            Licensed Software beyond the terms hereof. Licensee shall be
            responsible for any and all actions and omissions of its Designated
            Users relating to the Licensed Software and use thereof.

            Any use of Licensed Software beyond the provisions of this
            Agreement is strictly prohibited and requires an additional license
            from The Qt Company."

    V.      Section 12 shall be replaced in its entirety to read as follows:

            "This Agreement shall enter into force upon due acceptance by both
            Parties and remain in force for the Term, unless and until
            terminated pursuant to the terms of Section 12.

            Upon termination of the Agreement, Licensee shall cease using the
            Licensed Software. All other copies of Licensed Software in the
            possession or control of Licensee must be erased or destroyed. An
            officer of Licensee must, upon request, promptly deliver to The Qt
            Company a written confirmation that this has occurred."

Except for the modifications specified above, this Appendix carries no change
to the terms of the Agreement which shall remain in full force.

