#ifndef TTKCROSSLINELABEL_H
#define TTKCROSSLINELABEL_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include <QWidget>
#include "ttkmoduleexport.h"

class QProcess;

/*!
* <AUTHOR> <<EMAIL>>
*/
class TTK_MODULE_EXPORT TTKCrossLineLabel : public QWidget
{
    Q_OBJECT
    TTK_DECLARE_MODULE(TTKCrossLineLabel)
public:
    explicit TTKCrossLineLabel(QWidget *parent = nullptr);

    void setWidth(int w);
    void setColor(const QColor &color);

    virtual QSize sizeHint() const override final;

private:
    virtual void mouseMoveEvent(QMouseEvent *event) override final;
    virtual void mouseReleaseEvent(QMouseEvent *event) override final;
    virtual void paintEvent(QPaintEvent *event) override final;

    int m_width;
    QColor m_color;
    QPoint m_pos;

};

#endif // TTKCROSSLINELABEL_H
