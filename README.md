# TTKWidgetTools
![TTKWidgetTools](https://img.shields.io/badge/Greedysky-TTKWidgetTools-green.svg?style=flat-square)
![Version](https://img.shields.io/github/v/release/Greedysky/TTKWidgetTools?style=flat-square&label=Version)
![License](https://img.shields.io/badge/License-GPL%20V3-yellowgreen.svg?style=flat-square)
![License](https://img.shields.io/badge/License-LGPL%20V3-yellow.svg?style=flat-square)

![LOGO](TTKResource/logo_banner.png?raw=true)

### **|[About](#usage)|[License](#license)|[Download](#download)|[Features](#features)|[Screenshots](#screenshots)|[Copyright](#copyright)|[Contribute](#how-to-contribute)|**

Usage
--------
**TTKWidgetTools 是基于 Qt 的自定义控件集合，支持Windows和Linux平台.**

## CI Status
 * Qt stable version is tested on Qt4.x for windows-mingw 32bit
 * Qt stable version is tested on Qt4.x for ubuntu-linux-gcc 64bit
 * Qt stable version is tested on Qt5.x for windows-msvc 32bit
 * Qt stable version is tested on Qt5.x for windows-msvc 64bit
 * Qt stable version is tested on Qt5.x for windows-mingw 32bit
 * Qt stable version is tested on Qt5.x for ubuntu-linux-gcc 64bit
 * Qt stable version is tested on Qt6.x for windows-mingw 64bit
 * Qt stable version is tested on Qt6.x for ubuntu-linux-gcc 64bit

| Platform | Qt & Compiler               | Status                                                                 |
| :---:    | :---:                       | :---:                                                                  |
| Linux    | 4.x GCC & 5.x GCC & 6.x GCC | ![Linux](https://img.shields.io/badge/build-passing-brightgreen.svg)   |
| Windows  | 4.x GCC & 5.x GCC & 6.x GCC | ![Windows](https://img.shields.io/badge/build-passing-brightgreen.svg) |
| Windows  | 5.x MSVC                    | ![Windows](https://img.shields.io/badge/build-passing-brightgreen.svg) |
| OS X     | Clang                       | ![OSX](https://img.shields.io/badge/build-unknown-lightgrey.svg)       |

# For Developers

License
--------
TTKWidgetTools is free software licensed under the term of [Licence LGPL v3](LICENSE). If you use TTKWidgetTools or its constituent libraries, you must adhere to the terms of the license in question.

Download
--------
Gitee URL: **<u>https://gitee.com/Greedysky/TTKWidgetTools</u>**

Github URL: **<u>https://github.com/Greedysky/TTKWidgetTools</u>**

Features
--------
### Button
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Button | TTKCheckButtonWidget |   Finish   |
| Button | TTKFlatButtonWidget |   Finish   |
| Button | TTKRadioButtonWidget |   Finish   |
| Button | TTKToggleWidget |   Finish   |
| Button | TTKToolMenuWidget |   Finish   |
| Button | TTKColorButtonWidget |   Finish   |

### Label
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Label | TTKCircleClickLabel |   Finish   |
| Label | TTKCodeAreaLabel |   Finish   |
| Label | TTKLedPageLabel |   Finish   |
| Label | TTKMarqueeLabel |   Finish   |
| Label | TTKRoundAnimationLabel |   Finish   |
| Label | TTKSplitItemLabel | Finish     |
| Label | TTKToastLabel |   Finish   |
| Label | TTKTransitionAnimationLabel |   Finish   |
| Label | TTKBarRulerLabel |   Finish   |
| Label | TTKBatteryLabel | Finish     |
| Label | TTKCloudPanelLabel |   Finish   |
| Label | TTKCPUMemoryLabel |   Finish   |
| Label | TTKAntLineLabel |   Finish   |
| Label | TTKTileBackgroundLabel |   Finish   |
| Label | TTKLightPointLabel |   Finish   |
| Label | TTKScanLabel |   Finish   |
| Label | TTKCrossLineLabel |   Finish   |
| Label | TTKNetTrafficLabel |   Finish   |
| Label | TTKLcdLabel |   Finish   |

### LineEdit
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| LineEdit | TTKIpEditWidget |   Finish   |
| LineEdit | TTKLineEditWidget |   Finish   |

### Meter
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Meter | TTKPaintMeterWidget |   Finish   |
| Meter | TTKRadarMeterWidget |   Finish   |
| Meter | TTKSpeedMeterWidget |   Finish   |
| Meter | TTKTimeMeterWidget |   Finish   |
| Meter | TTKCarMeterWidget |   Finish   |
| Meter | TTKSpeedRingMeterWidget |   Finish   |
| Meter | TTKDialMeterWidget |   Finish   |
| Meter | TTKCompassMeterWidget |   Finish   |
| Meter | TTKProgressMeterWidget |   Finish   |
| Meter | TTKPercentMeterWidget |   Finish   |
| Meter | TTKArcMeterWidget |   Finish   |
| Meter | TTKPanelMeterWidget |   Finish   |
| Meter | TTKMiniMeterWidget |   Finish   |
| Meter | TTKRoundMeterWidget |   Finish   |
| Meter | TTKClockMeterWidget |   Finish   |
| Meter | TTKTemperatureMeterWidget |   Finish   |

### Progress
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Progress | TTKAnimationProgressWidget |   Finish   |
| Progress | TTKCircleProgressWidget |   Finish   |
| Progress | TTKCircleWaitProgressWidget |   Finish   |
| Progress | TTKCircularProgressWidget |   Finish   |
| Progress | TTKGifProgressWidget |   Finish   |
| Progress | TTKProgressWidget |   Finish   |
| Progress | TTKRadiusProgressWidget |   Finish   |
| Progress | TTKRingsMapProgressWidget |   Finish   |
| Progress | TTKRingsProgressWidget |   Finish   |
| Progress | TTKRoundProgressWidget |   Finish   |
| Progress | TTKRingProgressWidget |   Finish   |
| Progress | TTKWaveProgressWidget |   Finish   |
| Progress | TTKPieWaitProgressWidget |   Finish   |
| Progress | TTKLineWaitProgressWidget |   Finish   |
| Progress | TTKDonutWaitProgressWidget |   Finish   |
| Progress | TTKZoomWaitProgressWidget |   Finish   |

### Slider
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Slider | TTKMovingLabelSlider |   Finish   |
| Slider | TTKShiningSlider |   Finish   |
| Slider | TTKStyleSlider |   Finish   |

### Title
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Title | TTKFunctionAnimationWidget |   Finish   |
| Title | TTKFunctionListHWidget |   Finish   |
| Title | TTKFunctionListVWidget |   Finish   |
| Title | TTKFunctionNormalWidget |   Finish   |
| Title | TTKFunctionToolboxWidget |   Finish   |
| Title | TTKFunctionNavigationWidget |   Finish   |

### Widget
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Widget | TTKAnimation2StackedWidget |   Finish   |
| Widget | TTKAnimationStackedWidget |   Finish   |
| Widget | TTKColorTableWidget |   Finish   |
| Widget | TTKGrabItemWidget |   Finish   |
| Widget | TTKLayoutAnimationWidget |   Finish   |
| Widget | TTKPictureBannerWidget |   Finish   |
| Widget | TTKPictureFlowWidget |   Finish   |
| Widget | TTKSmoothMovingTableWidget |   Finish   |
| Widget | TTKCustomPieWidget |   Finish   |
| Widget | TTKCustomRingWidget |   Finish   |
| Widget | TTKCalendarWidget |   Finish   |
| Widget | TTKPuzzleWidget |   Finish   |

### Window
--------
| Module | Description       | Status        |
| :---:    | :---:               | :---:         |
| Window | TTKColorDialog |   Finish   |
| Window | TTKMoveDialog |   Finish   |
| Window | TTKMoveResizeWidget |   Finish   |
| Window | TTKMoveWidget |   Finish   |
| Window | TTKSplashScreen |   Finish   |
| Window | TTKAnSplashScreen |   Finish   |
| Window | TTKNotifyWindow |   Finish   |

Screenshots
--------
![Demo](TTKResource/example/Button/checkButtonWidget.png?raw=true)

Copyright
--------
 * This file is part of the TTK Widget Tools project.
 * Copyright (C) 2015 - 2025 Greedysky Studio.
 * Mail: <EMAIL>.

How To Contribute
--------
 * Fork this project on github and make a branch. Commit in that branch, and push, then create a pull request to be reviewed and merged.
 * Create an issue if you have any problem when using project or you find a bug, etc.
 * What you can do: translation, write document, wiki, find or fix bugs, give your idea for this project etc.
 * If you want to join the project developed together, please send e-mail to me.
