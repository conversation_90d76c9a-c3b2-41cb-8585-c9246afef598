#ifndef TTKLEXICALCAST_H
#define TTKLEXICALCAST_H

/***************************************************************************
 * This file is part of the TTK Library Module project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include "ttkmoduleexport.h"

/*! @brief The class of the ttk lexical cast.
 * <AUTHOR> <<EMAIL>>
 */
namespace TTK
{
    template <typename _Output, typename _Input>
    _Output lexical_cast(const _Input &input)
    {
        std::stringstream s;

        _Output ouput;
        if(!(s << input && s >> ouput && s.eof()))
        {
            throw std::bad_cast();
        }
        return ouput;
    }
}

#ifdef TTK_CAST
#  define TTKLexicalCast(x, y) (TTK::lexical_cast<x>(y))
#else
#  define TTKLexicalCast(x, y) ((x)(y))
#endif

// compatiblity for std lexical_cast
namespace std
{
using namespace TTK;
}

#endif // TTKLEXICALCAST_H
