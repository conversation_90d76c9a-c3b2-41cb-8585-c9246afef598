#ifndef TTKLABELINCLUDE_H
#define TTKLABELINCLUDE_H

/***************************************************************************
 * This file is part of the TTK Widget Tools project
 * Copyright (C) 2015 - 2025 Greedysky Studio

 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.

 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public License along
 * with this program; If not, see <http://www.gnu.org/licenses/>.
 ***************************************************************************/

#include "antLineLabel/ttkantlinelabelproperty.h"
#include "barRulerLabel/ttkbarrulerlabelproperty.h"
#include "batteryLabel/ttkbatterylabelproperty.h"
#include "circleClickLabel/ttkcircleclicklabelproperty.h"
#include "cloudPanelLabel/ttkcloudpanellabelproperty.h"
#include "codeAreaLabel/ttkcodearealabelproperty.h"
#include "cpuMemoryLabel/ttkcpumemorylabelproperty.h"
#include "crossLineLabel/ttkcrosslinelabelproperty.h"
#include "heatMapLabel/ttkheatmaplabelproperty.h"
#include "lcdLabel/ttklcdlabelproperty.h"
#include "ledPageLabel/ttkledpagelabelproperty.h"
#include "lightPointLabel/ttklightpointlabelproperty.h"
#include "marqueeLabel/ttkmarqueelabelproperty.h"
#include "netTrafficLabel/ttknettrafficlabelproperty.h"
#include "roundAnimationLabel/ttkroundanimationlabelproperty.h"
#include "scanLabel/ttkscanlabelproperty.h"
#include "splitItemLabel/ttksplititemlabelproperty.h"
#include "tileBackgroundLabel/ttktilebackgroundlabelproperty.h"
#include "toastLabel/ttktoastlabelproperty.h"
#include "transitionAnimationLabel/ttktransitionanimationlabelproperty.h"

#endif // TTKLABELINCLUDE_H
